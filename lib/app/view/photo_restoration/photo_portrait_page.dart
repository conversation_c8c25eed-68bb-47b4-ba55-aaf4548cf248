import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_portrait_provide.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_data.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class PhotoPortraitPage extends ConsumerWidget {
  const PhotoPortraitPage({super.key});

  // 模拟数据 - 证件照Banner数据
  // List<PhotoPortraitBanner> get mockBannerData => [
  //       PhotoPortraitBanner(
  //         bannerUrl: photoPortraitIdPhoto,
  //         caseId: 1,
  //         sort: 1,
  //       ),
  //       PhotoPortraitBanner(
  //         bannerUrl: photoPortraitIdPhoto,
  //         caseId: 2,
  //         sort: 2,
  //       ),
  //       PhotoPortraitBanner(
  //         bannerUrl: photoPortraitIdPhoto,
  //         caseId: 3,
  //         sort: 3,
  //       ),
  //     ];

  // 模拟数据 - 写真分类数据
  List<PhotoPortraitCategory> get mockCategoryData =>
      PhotoPortraitData.mockCategoryData;

  Widget _buildHeader(List<PhotoPortraitBanner> bannerData) {
    return SizedBox(
      height: 80,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: bannerData.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final banner = bannerData[index];
          return _buildBannerItem(banner, index);
        },
      ),
    );
  }

  Widget _buildBannerItem(PhotoPortraitBanner banner, int index) {
    return GestureDetector(
        onTap: () => _handleBannerTap(banner),
        child: CachedNetworkImage(
          imageUrl: banner.bannerUrl ?? "",
          fit: BoxFit.cover,
        ));
  }

  /// 处理Banner点击事件
  void _handleBannerTap(PhotoPortraitBanner banner) {
    // RouterUtil.checkLogin(
    //     // 这里需要context，但在ConsumerWidget中无法直接获取
    //     // 实际使用时需要传入context或使用其他方式处理
    //     // context,
    //     // call: () {
    //     //   // 根据jumpId跳转到对应的写真分类详情页
    //     //   // 这里可以根据实际需求实现跳转逻辑
    //     //   debugPrint("跳转到写真分类详情页，jumpId: ${banner.jumpId}");
    //     // },
    //     );
  }

  Widget _buildCategorySection(
      BuildContext context, PhotoPortraitCategory? category) {
    if (category == null) {
      return const SizedBox.shrink();
    }

    final details = category.details ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category.caseName ?? "",
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
              GestureDetector(
                onTap: () => _handleMoreTap(context, category.id),
                child: const Row(
                  children: [
                    Text(
                      "更多",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 8,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 160,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: details.length,
            separatorBuilder: (context, index) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final detail = details[index];
              return _buildCategoryItem(detail);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryItem(PhotoPortraitCategoryDetail detail) {
    return GestureDetector(
      onTap: () => _handleCategoryItemTap(detail),
      child: SizedBox(
        width: 110,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: CachedNetworkImage(
            imageUrl: detail.caseImage ?? "",
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey,
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey,
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.white54,
                  size: 32,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理"更多"按钮点击事件
  void _handleMoreTap(BuildContext context, int? categoryId) {
    if (categoryId != null) {
      // 传递完整的分类数据和ID
      context.push('/$photoPortraitCategoryPage', extra: {
        'categoryId': categoryId,
        'categories': mockCategoryData, // 暂时保持使用模拟数据，后续可以改为从provider获取
      });
    }
  }

  /// 处理分类项目点击事件
  void _handleCategoryItemTap(PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}");
  }

  /// 构建Banner头部组件
  Widget _buildBannerHeader( WidgetRef ref,) {
    final bannerAsync = ref.watch(fetchPhotoPortraitBannerProvider);
    return bannerAsync.when(
      data: (bannerList) {
        if (bannerList == null || bannerList.isEmpty) {
          return Center(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: Text(
                "暂无模版数据",
                style: TextStyle(color: Colors.white60),
              ),
            ),
          );
        }
        return SliverToBoxAdapter(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 14),
            child: _buildHeader(bannerList),
          ),
        );
      },
      loading: () => const SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(20.0),
            child: CircularProgressIndicator(),
          ),
        ),
      ),
      error: (error, stackTrace) => SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Text(
              "加载失败: $error",
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryAsync = ref.watch(fetchphotoPortraitCategoryListProvider);

    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: true,
          toolbarHeight: 44.h,
          title: const Text(
            "真人写真",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          leading: const Leading(
            color: Colors.white,
          ),
          actions: [
            InkWell(
              onTap: () {},
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  color: const Color(0x30FFFFFF),
                  borderRadius: BorderRadius.circular(13),
                ),
                child: const Text(
                  "生成记录",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: Column(
          children: [
            _buildBannerHeader(ref),
            categoryAsync.when(
              data: (categoryList) {
                if (categoryList == null || categoryList.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.0),
                      child: Text(
                        "暂无模版数据",
                        style: TextStyle(color: Colors.white60),
                      ),
                    ),
                  );
                }
                return ListView.builder(
                  itemCount: categoryList.length,
                  itemBuilder: (context, index) {
                    final category = categoryList[index];
                    return _buildCategorySection(context, category);
                  },
                );
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (Object error, StackTrace stackTrace) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Text(
                      "加载失败: $error",
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                );
              },
            ),
          ],
        ));
  }
}
